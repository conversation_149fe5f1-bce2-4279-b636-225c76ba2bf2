{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.16.3", "sequelize": "^6.37.7"}, "devDependencies": {"nodemon": "^3.0.0"}}
const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const { User } = require('../models');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socketId
  }

  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"]
      }
    });

    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        if (!token) {
          return next(new Error('Authentication error'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findByPk(decoded.id);
        
        if (!user) {
          return next(new Error('User not found'));
        }

        socket.userId = user.id;
        socket.userRole = user.role;
        next();
      } catch (error) {
        next(new Error('Authentication error'));
      }
    });

    this.io.on('connection', (socket) => {
      console.log(`User ${socket.userId} connected`);
      
      // Store user connection
      this.connectedUsers.set(socket.userId, socket.id);
      
      // Join user to their personal room
      socket.join(`user_${socket.userId}`);
      
      // Join role-based rooms
      socket.join(`role_${socket.userRole}`);

      // Handle disconnection
      socket.on('disconnect', () => {
        console.log(`User ${socket.userId} disconnected`);
        this.connectedUsers.delete(socket.userId);
      });

      // Handle joining job-specific rooms (for real-time application updates)
      socket.on('join_job', (jobId) => {
        socket.join(`job_${jobId}`);
      });

      socket.on('leave_job', (jobId) => {
        socket.leave(`job_${jobId}`);
      });

      // Handle typing indicators for chat
      socket.on('typing_start', (data) => {
        socket.to(data.roomId).emit('user_typing', {
          userId: socket.userId,
          isTyping: true
        });
      });

      socket.on('typing_stop', (data) => {
        socket.to(data.roomId).emit('user_typing', {
          userId: socket.userId,
          isTyping: false
        });
      });
    });

    return this.io;
  }

  // Send notification to specific user
  sendToUser(userId, event, data) {
    const socketId = this.connectedUsers.get(userId);
    if (socketId) {
      this.io.to(socketId).emit(event, data);
      return true;
    }
    return false;
  }

  // Send notification to user's room
  sendToUserRoom(userId, event, data) {
    this.io.to(`user_${userId}`).emit(event, data);
  }

  // Send to all users with specific role
  sendToRole(role, event, data) {
    this.io.to(`role_${role}`).emit(event, data);
  }

  // Send to all users following a specific job
  sendToJobFollowers(jobId, event, data) {
    this.io.to(`job_${jobId}`).emit(event, data);
  }

  // Broadcast to all connected users
  broadcast(event, data) {
    this.io.emit(event, data);
  }

  // Application-specific events
  notifyNewApplication(employerId, applicationData) {
    this.sendToUser(employerId, 'new_application', {
      type: 'new_application',
      title: 'New Job Application',
      message: `${applicationData.candidateName} applied for ${applicationData.jobTitle}`,
      data: applicationData,
      timestamp: new Date()
    });
  }

  notifyApplicationStatusUpdate(candidateId, statusData) {
    this.sendToUser(candidateId, 'application_status_update', {
      type: 'application_status_update',
      title: 'Application Status Updated',
      message: `Your application for ${statusData.jobTitle} has been ${statusData.status}`,
      data: statusData,
      timestamp: new Date()
    });
  }

  notifyJobMatch(userId, jobData) {
    this.sendToUser(userId, 'job_match', {
      type: 'job_match',
      title: 'New Job Match',
      message: `We found a job that matches your profile: ${jobData.title}`,
      data: jobData,
      timestamp: new Date()
    });
  }

  notifyInterviewScheduled(candidateId, interviewData) {
    this.sendToUser(candidateId, 'interview_scheduled', {
      type: 'interview_scheduled',
      title: 'Interview Scheduled',
      message: `Interview scheduled for ${interviewData.jobTitle} on ${interviewData.date}`,
      data: interviewData,
      timestamp: new Date()
    });
  }

  notifyProfileViewed(userId, viewerData) {
    this.sendToUser(userId, 'profile_viewed', {
      type: 'profile_viewed',
      title: 'Profile Viewed',
      message: `${viewerData.companyName} viewed your profile`,
      data: viewerData,
      timestamp: new Date()
    });
  }

  notifyJobExpiring(employerId, jobData) {
    this.sendToUser(employerId, 'job_expiring', {
      type: 'job_expiring',
      title: 'Job Posting Expiring',
      message: `Your job posting "${jobData.title}" will expire in ${jobData.daysLeft} days`,
      data: jobData,
      timestamp: new Date()
    });
  }

  // System-wide notifications
  notifySystemMaintenance(maintenanceData) {
    this.broadcast('system_maintenance', {
      type: 'system_maintenance',
      title: 'Scheduled Maintenance',
      message: `System maintenance scheduled for ${maintenanceData.date}`,
      data: maintenanceData,
      timestamp: new Date()
    });
  }

  notifyNewFeature(featureData) {
    this.broadcast('new_feature', {
      type: 'new_feature',
      title: 'New Feature Available',
      message: `Check out our new feature: ${featureData.name}`,
      data: featureData,
      timestamp: new Date()
    });
  }

  // Get online users count
  getOnlineUsersCount() {
    return this.connectedUsers.size;
  }

  // Get online users by role
  getOnlineUsersByRole(role) {
    const sockets = this.io.sockets.sockets;
    let count = 0;
    
    sockets.forEach(socket => {
      if (socket.userRole === role) {
        count++;
      }
    });
    
    return count;
  }

  // Check if user is online
  isUserOnline(userId) {
    return this.connectedUsers.has(userId);
  }
}

module.exports = new SocketService();

import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import SimpleJobSeekerDashboard from '../components/dashboard/SimpleJobSeekerDashboard';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();

  if (user?.role === 'jobseeker') {
    return <SimpleJobSeekerDashboard />;
  }

  // Default dashboard for other roles
  return <SimpleJobSeekerDashboard />;
};

export default DashboardPage;

'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('user_profiles', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        unique: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      firstName: {
        type: Sequelize.STRING,
        allowNull: false
      },
      lastName: {
        type: Sequelize.STRING,
        allowNull: false
      },
      phone: {
        type: Sequelize.STRING
      },
      location: {
        type: Sequelize.STRING
      },
      bio: {
        type: Sequelize.TEXT
      },
      profilePicture: {
        type: Sequelize.STRING
      },
      resumeUrl: {
        type: Sequelize.STRING
      },
      portfolioUrl: {
        type: Sequelize.STRING
      },
      linkedinUrl: {
        type: Sequelize.STRING
      },
      githubUrl: {
        type: Sequelize.STRING
      },
      expectedSalary: {
        type: Sequelize.DECIMAL(10, 2)
      },
      currentSalary: {
        type: Sequelize.DECIMAL(10, 2)
      },
      experienceLevel: {
        type: Sequelize.ENUM('entry', 'junior', 'mid', 'senior', 'lead', 'executive'),
        defaultValue: 'entry'
      },
      availability: {
        type: Sequelize.ENUM('immediate', 'two_weeks', 'one_month', 'three_months'),
        defaultValue: 'immediate'
      },
      workType: {
        type: Sequelize.ENUM('full_time', 'part_time', 'contract', 'freelance', 'internship'),
        defaultValue: 'full_time'
      },
      remotePreference: {
        type: Sequelize.ENUM('remote', 'hybrid', 'onsite', 'no_preference'),
        defaultValue: 'no_preference'
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      lastActiveAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add indexes
    await queryInterface.addIndex('user_profiles', ['userId']);
    await queryInterface.addIndex('user_profiles', ['location']);
    await queryInterface.addIndex('user_profiles', ['experienceLevel']);
    await queryInterface.addIndex('user_profiles', ['isActive']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('user_profiles');
  }
};

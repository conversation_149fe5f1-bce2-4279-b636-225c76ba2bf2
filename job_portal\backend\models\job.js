'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Job extends Model {
    static associate(models) {
      // Belongs to Company
      Job.belongsTo(models.Company, {
        foreignKey: 'companyId',
        as: 'company'
      });

      // Has many Applications
      Job.hasMany(models.Application, {
        foreignKey: 'jobId',
        as: 'applications'
      });

      // Many-to-many with Skills
      Job.belongsToMany(models.Skill, {
        through: 'JobSkills',
        foreignKey: 'jobId',
        as: 'skills'
      });

      // Has many SavedJobs
      Job.hasMany(models.SavedJob, {
        foreignKey: 'jobId',
        as: 'savedBy'
      });
    }
  }

  Job.init({
    companyId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    shortDescription: {
      type: DataTypes.STRING(500)
    },
    location: {
      type: DataTypes.STRING,
      allowNull: false
    },
    isRemote: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    workType: {
      type: DataTypes.ENUM('full_time', 'part_time', 'contract', 'freelance', 'internship'),
      defaultValue: 'full_time'
    },
    experienceLevel: {
      type: DataTypes.ENUM('entry', 'junior', 'mid', 'senior', 'lead', 'executive'),
      defaultValue: 'mid'
    },
    salaryMin: {
      type: DataTypes.DECIMAL(10, 2)
    },
    salaryMax: {
      type: DataTypes.DECIMAL(10, 2)
    },
    salaryCurrency: {
      type: DataTypes.STRING(3),
      defaultValue: 'USD'
    },
    salaryPeriod: {
      type: DataTypes.ENUM('hourly', 'daily', 'weekly', 'monthly', 'yearly'),
      defaultValue: 'yearly'
    },
    requirements: {
      type: DataTypes.ARRAY(DataTypes.TEXT),
      defaultValue: []
    },
    responsibilities: {
      type: DataTypes.ARRAY(DataTypes.TEXT),
      defaultValue: []
    },
    benefits: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      defaultValue: []
    },
    applicationDeadline: {
      type: DataTypes.DATE
    },
    startDate: {
      type: DataTypes.DATE
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    isFeatured: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    viewCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    applicationCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    category: {
      type: DataTypes.STRING
    },
    tags: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      defaultValue: []
    }
  }, {
    sequelize,
    modelName: 'Job',
    tableName: 'jobs',
    timestamps: true,
    indexes: [
      {
        fields: ['companyId']
      },
      {
        fields: ['location']
      },
      {
        fields: ['workType']
      },
      {
        fields: ['experienceLevel']
      },
      {
        fields: ['isActive']
      },
      {
        fields: ['isFeatured']
      },
      {
        fields: ['category']
      },
      {
        fields: ['createdAt']
      }
    ]
  });

  return Job;
};
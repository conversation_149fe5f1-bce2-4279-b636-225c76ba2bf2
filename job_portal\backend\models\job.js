'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Job extends Model {
    static associate(models) {
      // Belongs to Company
      Job.belongsTo(models.Company, {
        foreignKey: 'companyId',
        as: 'company'
      });

      // Has many Applications
      Job.hasMany(models.Application, {
        foreignKey: 'jobId',
        as: 'applications'
      });
    }
  }

  Job.init({
    companyId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    location: {
      type: DataTypes.STRING,
      allowNull: false
    },
    salary: {
      type: DataTypes.STRING
    },
    requirements: {
      type: DataTypes.TEXT
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    }
  }, {
    sequelize,
    modelName: 'Job',
    tableName: 'jobs',
    timestamps: true,
    indexes: [
      {
        fields: ['companyId']
      },
      {
        fields: ['location']
      },
      {
        fields: ['workType']
      },
      {
        fields: ['experienceLevel']
      },
      {
        fields: ['isActive']
      },
      {
        fields: ['isFeatured']
      },
      {
        fields: ['category']
      },
      {
        fields: ['createdAt']
      }
    ]
  });

  return Job;
};
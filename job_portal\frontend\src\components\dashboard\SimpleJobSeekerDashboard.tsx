import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Button
} from '@mui/material';
import {
  Work as WorkIcon,
  Person as PersonIcon
} from '@mui/icons-material';

const SimpleJobSeekerDashboard: React.FC = () => {

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Job Seeker Dashboard
      </Typography>

      {/* Welcome Message */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center" mb={2}>
            <PersonIcon color="primary" sx={{ mr: 2 }} />
            <Typography variant="h6">
              Welcome to Your Dashboard
            </Typography>
          </Box>
          <Typography variant="body1" color="text.secondary">
            Here you can manage your job applications, view job recommendations, and track your progress.
          </Typography>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Quick Actions
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={2}>
            <Button variant="contained" startIcon={<WorkIcon />}>
              Browse Jobs
            </Button>
            <Button variant="outlined">
              View Applications
            </Button>
            <Button variant="outlined">
              Update Profile
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Stats Placeholder */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Your Activity
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Application statistics and job recommendations will appear here once you start applying for jobs.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default SimpleJobSeekerDashboard;

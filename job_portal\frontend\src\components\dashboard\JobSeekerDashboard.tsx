import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Button,
  Paper,
  Divider
} from '@mui/material';
import {
  Work as WorkIcon,
  Bookmark as BookmarkIcon,
  Notifications as NotificationsIcon,
  TrendingUp as TrendingUpIcon,
  Person as PersonIcon
} from '@mui/icons-material';
// TODO: Install chart.js and react-chartjs-2 for charts
// import { Line } from 'react-chartjs-2';

interface DashboardData {
  profileCompletion: number;
  applicationStats: {
    pending: number;
    reviewed: number;
    interviewed: number;
    offered: number;
    hired: number;
    rejected: number;
    total: number;
  };
  recentApplications: any[];
  savedJobsCount: number;
  unreadNotifications: number;
  recommendedJobs: any[];
  applicationActivity: any[];
}

const JobSeekerDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/dashboard/jobseeker', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      if (data.success) {
        setDashboardData(data.dashboard);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    const colors: { [key: string]: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' } = {
      pending: 'warning',
      reviewed: 'info',
      interviewed: 'primary',
      offered: 'success',
      hired: 'success',
      rejected: 'error'
    };
    return colors[status] || 'default';
  };

  // TODO: Uncomment when chart.js is installed
  // const chartData = { ... };
  // const chartOptions = { ... };

  if (loading) {
    return <Box p={3}>Loading dashboard...</Box>;
  }

  if (!dashboardData) {
    return <Box p={3}>Error loading dashboard data</Box>;
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Job Seeker Dashboard
      </Typography>

      <Grid container spacing={3}>
        {/* Profile Completion */}
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <PersonIcon color="primary" />
                <Typography variant="h6" ml={1}>
                  Profile Completion
                </Typography>
              </Box>
              <Box display="flex" alignItems="center">
                <Box width="100%" mr={1}>
                  <LinearProgress 
                    variant="determinate" 
                    value={dashboardData.profileCompletion} 
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {dashboardData.profileCompletion}%
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" mt={1}>
                Complete your profile to get better job matches
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Total Applications */}
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <WorkIcon color="primary" />
                <Typography variant="h6" ml={1}>
                  Applications
                </Typography>
              </Box>
              <Typography variant="h3" color="primary">
                {dashboardData.applicationStats.total}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total applications submitted
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Saved Jobs */}
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <BookmarkIcon color="primary" />
                <Typography variant="h6" ml={1}>
                  Saved Jobs
                </Typography>
              </Box>
              <Typography variant="h3" color="primary">
                {dashboardData.savedJobsCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Jobs saved for later
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Notifications */}
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <NotificationsIcon color="primary" />
                <Typography variant="h6" ml={1}>
                  Notifications
                </Typography>
              </Box>
              <Typography variant="h3" color="primary">
                {dashboardData.unreadNotifications}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Unread notifications
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Application Status Breakdown */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Application Status
              </Typography>
              <Grid container spacing={2}>
                {Object.entries(dashboardData.applicationStats).map(([status, count]) => {
                  if (status === 'total') return null;
                  return (
                    <Grid item xs={6} key={status}>
                      <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Chip 
                          label={status.charAt(0).toUpperCase() + status.slice(1)} 
                          color={getStatusColor(status)}
                          size="small"
                        />
                        <Typography variant="body2" fontWeight="bold">
                          {count}
                        </Typography>
                      </Box>
                    </Grid>
                  );
                })}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Application Activity Chart */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Line data={chartData} options={chartOptions} />
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Applications */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Applications
              </Typography>
              <List>
                {dashboardData.recentApplications.map((application, index) => (
                  <React.Fragment key={application.id}>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar src={application.Job.Company.logo}>
                          {application.Job.Company.name.charAt(0)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={application.Job.title}
                        secondary={`${application.Job.Company.name} • ${application.Job.location}`}
                      />
                      <Chip 
                        label={application.status} 
                        color={getStatusColor(application.status)}
                        size="small"
                      />
                    </ListItem>
                    {index < dashboardData.recentApplications.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
              <Button variant="outlined" fullWidth sx={{ mt: 2 }}>
                View All Applications
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Recommended Jobs */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recommended Jobs
              </Typography>
              <List>
                {dashboardData.recommendedJobs.map((job, index) => (
                  <React.Fragment key={job.id}>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar src={job.Company.logo}>
                          {job.Company.name.charAt(0)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={job.title}
                        secondary={`${job.Company.name} • ${job.location}`}
                      />
                    </ListItem>
                    {index < dashboardData.recommendedJobs.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
              <Button variant="outlined" fullWidth sx={{ mt: 2 }}>
                View All Recommendations
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default JobSeekerDashboard;

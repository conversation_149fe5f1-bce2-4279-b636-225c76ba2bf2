import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Divider
} from '@mui/material';
import {
  Work as WorkIcon,
  People as PeopleIcon,
  Visibility as VisibilityIcon,
  TrendingUp as TrendingUpIcon,
  Business as BusinessIcon
} from '@mui/icons-material';
import { Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

interface EmployerDashboardData {
  company: any;
  jobStats: {
    active: number;
    inactive: number;
    totalApplications: number;
    totalViews: number;
  };
  recentApplications: any[];
  topJobs: any[];
  applicationStatusStats: {
    pending: number;
    reviewed: number;
    interviewed: number;
    offered: number;
    hired: number;
    rejected: number;
    total: number;
  };
  hiringFunnel: any[];
}

const EmployerDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<EmployerDashboardData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/dashboard/employer', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      if (data.success) {
        setDashboardData(data.dashboard);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    const colors: { [key: string]: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' } = {
      pending: 'warning',
      reviewed: 'info',
      interviewed: 'primary',
      offered: 'success',
      hired: 'success',
      rejected: 'error'
    };
    return colors[status] || 'default';
  };

  const applicationStatusChartData = {
    labels: Object.keys(dashboardData?.applicationStatusStats || {}).filter(key => key !== 'total'),
    datasets: [
      {
        data: Object.entries(dashboardData?.applicationStatusStats || {})
          .filter(([key]) => key !== 'total')
          .map(([, value]) => value),
        backgroundColor: [
          '#FF6384',
          '#36A2EB',
          '#FFCE56',
          '#4BC0C0',
          '#9966FF',
          '#FF9F40'
        ],
        hoverBackgroundColor: [
          '#FF6384',
          '#36A2EB',
          '#FFCE56',
          '#4BC0C0',
          '#9966FF',
          '#FF9F40'
        ]
      }
    ]
  };

  const jobPerformanceChartData = {
    labels: dashboardData?.topJobs.map(job => job.title.substring(0, 20) + '...') || [],
    datasets: [
      {
        label: 'Applications',
        data: dashboardData?.topJobs.map(job => job.applicationCount) || [],
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1
      },
      {
        label: 'Views',
        data: dashboardData?.topJobs.map(job => job.viewCount) || [],
        backgroundColor: 'rgba(255, 99, 132, 0.6)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1
      }
    ]
  };

  if (loading) {
    return <Box p={3}>Loading dashboard...</Box>;
  }

  if (!dashboardData) {
    return <Box p={3}>Error loading dashboard data</Box>;
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Employer Dashboard
      </Typography>

      {/* Company Info */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center">
            <Avatar 
              src={dashboardData.company.logo} 
              sx={{ width: 64, height: 64, mr: 2 }}
            >
              <BusinessIcon />
            </Avatar>
            <Box>
              <Typography variant="h5">
                {dashboardData.company.name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {dashboardData.company.industry} • {dashboardData.company.companySize} employees
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {dashboardData.company.headquarters}
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        {/* Key Metrics */}
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <WorkIcon color="primary" />
                <Typography variant="h6" ml={1}>
                  Active Jobs
                </Typography>
              </Box>
              <Typography variant="h3" color="primary">
                {dashboardData.jobStats.active}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {dashboardData.jobStats.inactive} inactive
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <PeopleIcon color="primary" />
                <Typography variant="h6" ml={1}>
                  Applications
                </Typography>
              </Box>
              <Typography variant="h3" color="primary">
                {dashboardData.jobStats.totalApplications}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total applications received
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <VisibilityIcon color="primary" />
                <Typography variant="h6" ml={1}>
                  Job Views
                </Typography>
              </Box>
              <Typography variant="h3" color="primary">
                {dashboardData.jobStats.totalViews}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total job views
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <TrendingUpIcon color="primary" />
                <Typography variant="h6" ml={1}>
                  Conversion Rate
                </Typography>
              </Box>
              <Typography variant="h3" color="primary">
                {dashboardData.jobStats.totalViews > 0 
                  ? ((dashboardData.jobStats.totalApplications / dashboardData.jobStats.totalViews) * 100).toFixed(1)
                  : 0}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Views to applications
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Application Status Distribution */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Application Status Distribution
              </Typography>
              <Box height={300}>
                <Doughnut 
                  data={applicationStatusChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'bottom'
                      }
                    }
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Top Performing Jobs */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Top Performing Jobs
              </Typography>
              <Box height={300}>
                <Bar 
                  data={jobPerformanceChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'top'
                      }
                    },
                    scales: {
                      y: {
                        beginAtZero: true
                      }
                    }
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Applications */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Applications
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Candidate</TableCell>
                      <TableCell>Job</TableCell>
                      <TableCell>Applied</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {dashboardData.recentApplications.map((application) => (
                      <TableRow key={application.id}>
                        <TableCell>
                          <Box display="flex" alignItems="center">
                            <Avatar sx={{ mr: 2 }}>
                              {application.User.name.charAt(0)}
                            </Avatar>
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {application.User.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {application.User.email}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>{application.Job.title}</TableCell>
                        <TableCell>
                          {new Date(application.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={application.status} 
                            color={getStatusColor(application.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Button size="small" variant="outlined">
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              <Button variant="outlined" fullWidth sx={{ mt: 2 }}>
                View All Applications
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Box display="flex" flexDirection="column" gap={2}>
                <Button variant="contained" fullWidth>
                  Post New Job
                </Button>
                <Button variant="outlined" fullWidth>
                  Manage Jobs
                </Button>
                <Button variant="outlined" fullWidth>
                  Search Candidates
                </Button>
                <Button variant="outlined" fullWidth>
                  Company Settings
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EmployerDashboard;

'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Application extends Model {
    static associate(models) {
      // Belongs to User (job seeker)
      Application.belongsTo(models.User, {
        foreignKey: 'userId',
        as: 'user'
      });

      // Belongs to Job
      Application.belongsTo(models.Job, {
        foreignKey: 'jobId',
        as: 'job'
      });
    }
  }

  Application.init({
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    jobId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    coverLetter: {
      type: DataTypes.TEXT
    },
    resumeUrl: {
      type: DataTypes.STRING
    },
    status: {
      type: DataTypes.ENUM('pending', 'reviewed', 'interviewed', 'offered', 'hired', 'rejected'),
      defaultValue: 'pending'
    }
  }, {
    sequelize,
    modelName: 'Application',
    tableName: 'job_applications',
    timestamps: true
  });

  return Application;
};
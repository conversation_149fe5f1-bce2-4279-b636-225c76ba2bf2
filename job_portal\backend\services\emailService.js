const nodemailer = require('nodemailer');
const handlebars = require('handlebars');
const fs = require('fs').promises;
const path = require('path');

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
  }

  async loadTemplate(templateName, data) {
    try {
      const templatePath = path.join(__dirname, '../templates/emails', `${templateName}.hbs`);
      const templateContent = await fs.readFile(templatePath, 'utf8');
      const template = handlebars.compile(templateContent);
      return template(data);
    } catch (error) {
      console.error('Error loading email template:', error);
      throw error;
    }
  }

  async sendEmail(to, subject, templateName, data) {
    try {
      const html = await this.loadTemplate(templateName, data);
      
      const mailOptions = {
        from: `"${process.env.APP_NAME}" <${process.env.SMTP_FROM}>`,
        to,
        subject,
        html
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', result.messageId);
      return result;
    } catch (error) {
      console.error('Error sending email:', error);
      throw error;
    }
  }

  // Welcome email for new users
  async sendWelcomeEmail(user) {
    const subject = `Welcome to ${process.env.APP_NAME}!`;
    const data = {
      name: user.name,
      email: user.email,
      role: user.role,
      appName: process.env.APP_NAME,
      appUrl: process.env.APP_URL
    };

    return this.sendEmail(user.email, subject, 'welcome', data);
  }

  // Application confirmation email
  async sendApplicationConfirmation(user, job, company) {
    const subject = `Application Submitted: ${job.title}`;
    const data = {
      candidateName: user.name,
      jobTitle: job.title,
      companyName: company.name,
      applicationDate: new Date().toLocaleDateString(),
      appUrl: process.env.APP_URL
    };

    return this.sendEmail(user.email, subject, 'application-confirmation', data);
  }

  // Application status update email
  async sendApplicationStatusUpdate(user, job, company, status, message = '') {
    const statusMessages = {
      reviewed: 'Your application is being reviewed',
      interviewed: 'You have been selected for an interview',
      offered: 'Congratulations! You have received a job offer',
      hired: 'Congratulations! You have been hired',
      rejected: 'Thank you for your interest'
    };

    const subject = `Application Update: ${job.title} - ${statusMessages[status]}`;
    const data = {
      candidateName: user.name,
      jobTitle: job.title,
      companyName: company.name,
      status: status,
      statusMessage: statusMessages[status],
      customMessage: message,
      appUrl: process.env.APP_URL
    };

    return this.sendEmail(user.email, subject, 'application-status-update', data);
  }

  // New application notification for employers
  async sendNewApplicationNotification(employer, candidate, job) {
    const subject = `New Application: ${job.title}`;
    const data = {
      employerName: employer.name,
      candidateName: candidate.name,
      candidateEmail: candidate.email,
      jobTitle: job.title,
      applicationDate: new Date().toLocaleDateString(),
      appUrl: process.env.APP_URL
    };

    return this.sendEmail(employer.email, subject, 'new-application', data);
  }

  // Job match notification
  async sendJobMatchNotification(user, jobs) {
    const subject = `New Job Matches Found!`;
    const data = {
      candidateName: user.name,
      jobCount: jobs.length,
      jobs: jobs.map(job => ({
        title: job.title,
        company: job.Company.name,
        location: job.location,
        url: `${process.env.APP_URL}/jobs/${job.id}`
      })),
      appUrl: process.env.APP_URL
    };

    return this.sendEmail(user.email, subject, 'job-matches', data);
  }

  // Password reset email
  async sendPasswordResetEmail(user, resetToken) {
    const subject = 'Password Reset Request';
    const data = {
      name: user.name,
      resetUrl: `${process.env.APP_URL}/reset-password?token=${resetToken}`,
      appName: process.env.APP_NAME
    };

    return this.sendEmail(user.email, subject, 'password-reset', data);
  }

  // Email verification
  async sendEmailVerification(user, verificationToken) {
    const subject = 'Verify Your Email Address';
    const data = {
      name: user.name,
      verificationUrl: `${process.env.APP_URL}/verify-email?token=${verificationToken}`,
      appName: process.env.APP_NAME
    };

    return this.sendEmail(user.email, subject, 'email-verification', data);
  }

  // Interview invitation
  async sendInterviewInvitation(candidate, job, company, interviewDetails) {
    const subject = `Interview Invitation: ${job.title}`;
    const data = {
      candidateName: candidate.name,
      jobTitle: job.title,
      companyName: company.name,
      interviewDate: interviewDetails.date,
      interviewTime: interviewDetails.time,
      interviewType: interviewDetails.type, // 'phone', 'video', 'in-person'
      location: interviewDetails.location,
      meetingLink: interviewDetails.meetingLink,
      contactPerson: interviewDetails.contactPerson,
      contactEmail: interviewDetails.contactEmail,
      instructions: interviewDetails.instructions,
      appUrl: process.env.APP_URL
    };

    return this.sendEmail(candidate.email, subject, 'interview-invitation', data);
  }

  // Weekly job digest
  async sendWeeklyJobDigest(user, jobs) {
    const subject = 'Your Weekly Job Digest';
    const data = {
      candidateName: user.name,
      jobCount: jobs.length,
      jobs: jobs.map(job => ({
        title: job.title,
        company: job.Company.name,
        location: job.location,
        salary: job.salaryMin && job.salaryMax ? 
          `$${job.salaryMin.toLocaleString()} - $${job.salaryMax.toLocaleString()}` : 
          'Salary not specified',
        url: `${process.env.APP_URL}/jobs/${job.id}`,
        postedDate: job.createdAt.toLocaleDateString()
      })),
      appUrl: process.env.APP_URL,
      unsubscribeUrl: `${process.env.APP_URL}/unsubscribe?email=${user.email}`
    };

    return this.sendEmail(user.email, subject, 'weekly-digest', data);
  }
}

module.exports = new EmailService();

'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Skill extends Model {
    static associate(models) {
      // Many-to-many relationship with UserProfile
      Skill.belongsToMany(models.UserProfile, {
        through: 'UserSkills',
        foreignKey: 'skillId',
        as: 'userProfiles'
      });
      
      // Many-to-many relationship with Job
      Skill.belongsToMany(models.Job, {
        through: 'JobSkills',
        foreignKey: 'skillId',
        as: 'jobs'
      });
    }
  }

  Skill.init({
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    category: {
      type: DataTypes.ENUM(
        'programming_languages',
        'frameworks',
        'databases',
        'tools',
        'soft_skills',
        'certifications',
        'languages'
      ),
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    }
  }, {
    sequelize,
    modelName: 'Skill',
    tableName: 'skills',
    timestamps: true,
    indexes: [
      {
        fields: ['name']
      },
      {
        fields: ['category']
      },
      {
        fields: ['isActive']
      }
    ]
  });

  return Skill;
};

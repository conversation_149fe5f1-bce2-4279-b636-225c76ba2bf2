'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const jobs = [
      // TechCorp Solutions Jobs
      {
        companyId: 1,
        title: 'Senior Full Stack Developer',
        description: 'We are looking for an experienced Full Stack Developer to join our engineering team. You will be responsible for developing and maintaining web applications using modern technologies like React, Node.js, and PostgreSQL. The ideal candidate has 5+ years of experience in web development and is passionate about creating scalable solutions.',
        location: 'San Francisco, CA',
        salary: '$120,000 - $150,000',
        requirements: 'Bachelor\'s degree in Computer Science or related field. 5+ years of experience with JavaScript, React, Node.js. Experience with databases and cloud platforms.',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        companyId: 1,
        title: 'DevOps Engineer',
        description: 'Join our DevOps team to help build and maintain our cloud infrastructure. You will work with AWS, Docker, Kubernetes, and CI/CD pipelines to ensure our applications run smoothly and scale efficiently.',
        location: 'San Francisco, CA',
        salary: '$110,000 - $140,000',
        requirements: 'Experience with <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>. Knowledge of infrastructure as code (Terraform). Strong scripting skills.',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        companyId: 1,
        title: 'Product Manager',
        description: 'Lead product development initiatives and work closely with engineering and design teams. Define product roadmaps, gather requirements, and ensure successful product launches.',
        location: 'San Francisco, CA',
        salary: '$130,000 - $160,000',
        requirements: 'MBA or equivalent experience. 3+ years in product management. Experience with agile methodologies and product analytics.',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },

      // StartupXYZ Jobs
      {
        companyId: 2,
        title: 'Frontend Developer',
        description: 'Build beautiful and responsive user interfaces for our fintech platform. Work with React, TypeScript, and modern CSS frameworks to create exceptional user experiences.',
        location: 'New York, NY',
        salary: '$90,000 - $120,000',
        requirements: 'Strong experience with React, TypeScript, HTML5, CSS3. Knowledge of responsive design and modern frontend tools.',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        companyId: 2,
        title: 'Backend Developer',
        description: 'Develop robust APIs and microservices for our payment processing platform. Work with Python, Django, and PostgreSQL in a fast-paced startup environment.',
        location: 'New York, NY',
        salary: '$100,000 - $130,000',
        requirements: 'Experience with Python, Django/Flask, PostgreSQL. Knowledge of API design and microservices architecture.',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        companyId: 2,
        title: 'Data Scientist',
        description: 'Analyze financial data to drive business insights and improve our algorithms. Work with large datasets and machine learning models to enhance our platform.',
        location: 'New York, NY',
        salary: '$115,000 - $145,000',
        requirements: 'PhD or Master\'s in Data Science, Statistics, or related field. Experience with Python, R, SQL, and machine learning frameworks.',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },

      // Global Solutions Inc Jobs
      {
        companyId: 3,
        title: 'Business Analyst',
        description: 'Work with clients to understand their business needs and translate them into technical requirements. Collaborate with development teams to deliver solutions.',
        location: 'Chicago, IL',
        salary: '$75,000 - $95,000',
        requirements: 'Bachelor\'s degree in Business, Economics, or related field. Strong analytical and communication skills. Experience with business process modeling.',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        companyId: 3,
        title: 'Project Manager',
        description: 'Lead cross-functional teams to deliver client projects on time and within budget. Manage project timelines, resources, and stakeholder communications.',
        location: 'Chicago, IL',
        salary: '$85,000 - $110,000',
        requirements: 'PMP certification preferred. 3+ years of project management experience. Strong leadership and communication skills.',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },

      // Innovation Labs Jobs
      {
        companyId: 4,
        title: 'Machine Learning Engineer',
        description: 'Develop and deploy machine learning models for various AI applications. Work with cutting-edge technologies and contribute to research publications.',
        location: 'Austin, TX',
        salary: '$125,000 - $155,000',
        requirements: 'Master\'s or PhD in Computer Science, AI, or related field. Experience with TensorFlow, PyTorch, and cloud ML platforms.',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        companyId: 4,
        title: 'Research Scientist',
        description: 'Conduct research in artificial intelligence and machine learning. Publish papers and present findings at top-tier conferences.',
        location: 'Austin, TX',
        salary: '$140,000 - $180,000',
        requirements: 'PhD in Computer Science, AI, or related field. Strong publication record. Experience with deep learning and NLP.',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },

      // Creative Digital Agency Jobs
      {
        companyId: 5,
        title: 'UX/UI Designer',
        description: 'Create intuitive and visually appealing designs for web and mobile applications. Work closely with clients and development teams.',
        location: 'Los Angeles, CA',
        salary: '$70,000 - $95,000',
        requirements: 'Bachelor\'s degree in Design or related field. Proficiency in Figma, Adobe Creative Suite. Strong portfolio demonstrating UX/UI skills.',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        companyId: 5,
        title: 'Digital Marketing Specialist',
        description: 'Develop and execute digital marketing campaigns across various channels. Analyze performance metrics and optimize campaigns for better ROI.',
        location: 'Los Angeles, CA',
        salary: '$55,000 - $75,000',
        requirements: 'Experience with Google Ads, Facebook Ads, SEO/SEM. Knowledge of analytics tools and marketing automation platforms.',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        companyId: 5,
        title: 'Content Creator',
        description: 'Create engaging content for social media, blogs, and marketing campaigns. Collaborate with design and marketing teams to develop compelling narratives.',
        location: 'Los Angeles, CA',
        salary: '$45,000 - $65,000',
        requirements: 'Strong writing skills. Experience with content management systems. Knowledge of social media platforms and content strategy.',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },

      // Additional Remote Jobs
      {
        companyId: 1,
        title: 'Remote Software Engineer',
        description: 'Work remotely as part of our distributed engineering team. Build scalable web applications and contribute to our open-source projects.',
        location: 'Remote',
        salary: '$100,000 - $130,000',
        requirements: 'Strong programming skills in JavaScript, Python, or Java. Experience with remote collaboration tools. Self-motivated and excellent communication skills.',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        companyId: 2,
        title: 'Remote Customer Success Manager',
        description: 'Help our clients succeed with our platform. Provide support, training, and strategic guidance to ensure customer satisfaction and retention.',
        location: 'Remote',
        salary: '$70,000 - $90,000',
        requirements: 'Experience in customer success or account management. Strong communication skills. Ability to work independently in a remote environment.',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    await queryInterface.bulkInsert('jobs', jobs, {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('jobs', null, {});
  }
};

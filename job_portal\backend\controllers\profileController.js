const { User<PERSON>ro<PERSON>le, User, Skill, Experience, Education } = require('../models');
const { Op } = require('sequelize');

// Get user profile
const getProfile = async (req, res) => {
  try {
    const { userId } = req.params;
    const requestingUserId = req.user.id;

    // Users can only view their own profile unless they're employers
    if (userId != requestingUserId && req.user.role !== 'employer') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const profile = await UserProfile.findOne({
      where: { userId },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email', 'role']
        },
        {
          model: Skill,
          as: 'skills',
          through: { attributes: ['proficiencyLevel', 'yearsOfExperience'] }
        },
        {
          model: Experience,
          as: 'experiences',
          order: [['startDate', 'DESC']]
        },
        {
          model: Education,
          as: 'education',
          order: [['startDate', 'DESC']]
        }
      ]
    });

    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
    }

    res.json({
      success: true,
      profile
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Create or update user profile
const updateProfile = async (req, res) => {
  try {
    const userId = req.user.id;
    const profileData = req.body;

    // Remove user-specific fields that shouldn't be updated here
    delete profileData.userId;
    delete profileData.id;

    const [profile, created] = await UserProfile.upsert({
      ...profileData,
      userId
    }, {
      returning: true
    });

    // Update last active timestamp
    await UserProfile.update(
      { lastActiveAt: new Date() },
      { where: { userId } }
    );

    res.json({
      success: true,
      message: created ? 'Profile created successfully' : 'Profile updated successfully',
      profile
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Add skill to profile
const addSkill = async (req, res) => {
  try {
    const userId = req.user.id;
    const { skillId, proficiencyLevel, yearsOfExperience } = req.body;

    const profile = await UserProfile.findOne({ where: { userId } });
    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
    }

    const skill = await Skill.findByPk(skillId);
    if (!skill) {
      return res.status(404).json({
        success: false,
        message: 'Skill not found'
      });
    }

    await profile.addSkill(skill, {
      through: {
        proficiencyLevel: proficiencyLevel || 'intermediate',
        yearsOfExperience: yearsOfExperience || 0
      }
    });

    res.json({
      success: true,
      message: 'Skill added to profile'
    });
  } catch (error) {
    console.error('Add skill error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Remove skill from profile
const removeSkill = async (req, res) => {
  try {
    const userId = req.user.id;
    const { skillId } = req.params;

    const profile = await UserProfile.findOne({ where: { userId } });
    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
    }

    const skill = await Skill.findByPk(skillId);
    if (!skill) {
      return res.status(404).json({
        success: false,
        message: 'Skill not found'
      });
    }

    await profile.removeSkill(skill);

    res.json({
      success: true,
      message: 'Skill removed from profile'
    });
  } catch (error) {
    console.error('Remove skill error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Search profiles (for employers)
const searchProfiles = async (req, res) => {
  try {
    if (req.user.role !== 'employer') {
      return res.status(403).json({
        success: false,
        message: 'Only employers can search profiles'
      });
    }

    const {
      skills,
      location,
      experienceLevel,
      workType,
      remotePreference,
      page = 1,
      limit = 10
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = { isActive: true };

    if (location) {
      whereClause.location = { [Op.iLike]: `%${location}%` };
    }
    if (experienceLevel) {
      whereClause.experienceLevel = experienceLevel;
    }
    if (workType) {
      whereClause.workType = workType;
    }
    if (remotePreference) {
      whereClause.remotePreference = remotePreference;
    }

    const includeClause = [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email']
      },
      {
        model: Skill,
        as: 'skills',
        through: { attributes: ['proficiencyLevel', 'yearsOfExperience'] }
      }
    ];

    // Filter by skills if provided
    if (skills) {
      const skillArray = Array.isArray(skills) ? skills : [skills];
      includeClause[1].where = {
        name: { [Op.in]: skillArray }
      };
      includeClause[1].required = true;
    }

    const { count, rows: profiles } = await UserProfile.findAndCountAll({
      where: whereClause,
      include: includeClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['lastActiveAt', 'DESC']]
    });

    res.json({
      success: true,
      profiles,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(count / limit),
        totalItems: count,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Search profiles error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

module.exports = {
  getProfile,
  updateProfile,
  addSkill,
  removeSkill,
  searchProfiles
};

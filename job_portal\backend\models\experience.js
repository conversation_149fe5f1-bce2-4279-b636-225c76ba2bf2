'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Experience extends Model {
    static associate(models) {
      Experience.belongsTo(models.UserProfile, {
        foreignKey: 'userProfileId',
        as: 'userProfile'
      });
    }
  }

  Experience.init({
    userProfileId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    jobTitle: {
      type: DataTypes.STRING,
      allowNull: false
    },
    companyName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    location: {
      type: DataTypes.STRING
    },
    startDate: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    endDate: {
      type: DataTypes.DATEONLY,
      validate: {
        isAfterStartDate(value) {
          if (value && value <= this.startDate) {
            throw new Error('End date must be after start date');
          }
        }
      }
    },
    isCurrent: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    description: {
      type: DataTypes.TEXT
    },
    achievements: {
      type: DataTypes.ARRAY(DataTypes.TEXT),
      defaultValue: []
    },
    technologies: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      defaultValue: []
    }
  }, {
    sequelize,
    modelName: 'Experience',
    tableName: 'experiences',
    timestamps: true,
    validate: {
      endDateOrCurrent() {
        if (!this.isCurrent && !this.endDate) {
          throw new Error('End date is required if not current position');
        }
        if (this.isCurrent && this.endDate) {
          throw new Error('End date should not be set for current position');
        }
      }
    },
    indexes: [
      {
        fields: ['userProfileId']
      },
      {
        fields: ['startDate']
      },
      {
        fields: ['isCurrent']
      }
    ]
  });

  return Experience;
};

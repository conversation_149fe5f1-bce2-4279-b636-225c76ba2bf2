import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Autocomplete,
  Slider,
  Typography,
  Button,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControlLabel,
  Checkbox,
  Switch
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Search as SearchIcon,
  Clear as ClearIcon
} from '@mui/icons-material';

interface SearchFilters {
  keywords: string;
  location: string;
  skills: string[];
  experienceLevel: string[];
  workType: string[];
  remoteWork: boolean;
  salaryRange: [number, number];
  companySize: string[];
  industry: string[];
  postedWithin: string;
  sortBy: string;
}

interface AdvancedJobSearchProps {
  onSearch: (filters: SearchFilters) => void;
  loading?: boolean;
}

const AdvancedJobSearch: React.FC<AdvancedJobSearchProps> = ({ onSearch, loading = false }) => {
  const [filters, setFilters] = useState<SearchFilters>({
    keywords: '',
    location: '',
    skills: [],
    experienceLevel: [],
    workType: [],
    remoteWork: false,
    salaryRange: [0, 200000],
    companySize: [],
    industry: [],
    postedWithin: '',
    sortBy: 'relevance'
  });

  const [skillOptions, setSkillOptions] = useState<string[]>([]);
  const [locationOptions, setLocationOptions] = useState<string[]>([]);

  useEffect(() => {
    // Fetch skill options from API
    fetchSkillOptions();
    fetchLocationOptions();
  }, []);

  const fetchSkillOptions = async () => {
    try {
      const response = await fetch('/api/skills');
      const data = await response.json();
      if (data.success) {
        setSkillOptions(data.skills.map((skill: any) => skill.name));
      }
    } catch (error) {
      console.error('Error fetching skills:', error);
    }
  };

  const fetchLocationOptions = async () => {
    try {
      const response = await fetch('/api/locations');
      const data = await response.json();
      if (data.success) {
        setLocationOptions(data.locations);
      }
    } catch (error) {
      console.error('Error fetching locations:', error);
    }
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSearch = () => {
    onSearch(filters);
  };

  const handleClearFilters = () => {
    setFilters({
      keywords: '',
      location: '',
      skills: [],
      experienceLevel: [],
      workType: [],
      remoteWork: false,
      salaryRange: [0, 200000],
      companySize: [],
      industry: [],
      postedWithin: '',
      sortBy: 'relevance'
    });
  };

  const experienceLevels = ['entry', 'junior', 'mid', 'senior', 'lead', 'executive'];
  const workTypes = ['full_time', 'part_time', 'contract', 'freelance', 'internship'];
  const companySizes = ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+'];
  const industries = [
    'Technology', 'Healthcare', 'Finance', 'Education', 'Manufacturing',
    'Retail', 'Consulting', 'Media', 'Government', 'Non-profit'
  ];
  const postedWithinOptions = [
    { value: '1', label: 'Last 24 hours' },
    { value: '7', label: 'Last week' },
    { value: '30', label: 'Last month' },
    { value: '90', label: 'Last 3 months' }
  ];

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Advanced Job Search
        </Typography>

        {/* Basic Search */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Keywords"
              placeholder="Job title, company, or keywords"
              value={filters.keywords}
              onChange={(e) => handleFilterChange('keywords', e.target.value)}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Autocomplete
              options={locationOptions}
              value={filters.location}
              onChange={(_, value) => handleFilterChange('location', value || '')}
              renderInput={(params) => (
                <TextField {...params} label="Location" placeholder="City, state, or remote" />
              )}
            />
          </Grid>
        </Grid>

        {/* Skills */}
        <Box sx={{ mb: 3 }}>
          <Autocomplete
            multiple
            options={skillOptions}
            value={filters.skills}
            onChange={(_, value) => handleFilterChange('skills', value)}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip variant="outlined" label={option} {...getTagProps({ index })} />
              ))
            }
            renderInput={(params) => (
              <TextField {...params} label="Skills" placeholder="Select skills" />
            )}
          />
        </Box>

        {/* Advanced Filters */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>Advanced Filters</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={3}>
              {/* Experience Level */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Experience Level</InputLabel>
                  <Select
                    multiple
                    value={filters.experienceLevel}
                    onChange={(e) => handleFilterChange('experienceLevel', e.target.value)}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {(selected as string[]).map((value) => (
                          <Chip key={value} label={value} size="small" />
                        ))}
                      </Box>
                    )}
                  >
                    {experienceLevels.map((level) => (
                      <MenuItem key={level} value={level}>
                        {level.charAt(0).toUpperCase() + level.slice(1)}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Work Type */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Work Type</InputLabel>
                  <Select
                    multiple
                    value={filters.workType}
                    onChange={(e) => handleFilterChange('workType', e.target.value)}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {(selected as string[]).map((value) => (
                          <Chip key={value} label={value.replace('_', ' ')} size="small" />
                        ))}
                      </Box>
                    )}
                  >
                    {workTypes.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type.replace('_', ' ').charAt(0).toUpperCase() + type.replace('_', ' ').slice(1)}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Remote Work */}
              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={filters.remoteWork}
                      onChange={(e) => handleFilterChange('remoteWork', e.target.checked)}
                    />
                  }
                  label="Remote Work Available"
                />
              </Grid>

              {/* Salary Range */}
              <Grid item xs={12} md={6}>
                <Typography gutterBottom>
                  Salary Range: ${filters.salaryRange[0].toLocaleString()} - ${filters.salaryRange[1].toLocaleString()}
                </Typography>
                <Slider
                  value={filters.salaryRange}
                  onChange={(_, value) => handleFilterChange('salaryRange', value)}
                  valueLabelDisplay="auto"
                  min={0}
                  max={300000}
                  step={5000}
                  valueLabelFormat={(value) => `$${value.toLocaleString()}`}
                />
              </Grid>

              {/* Company Size */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Company Size</InputLabel>
                  <Select
                    multiple
                    value={filters.companySize}
                    onChange={(e) => handleFilterChange('companySize', e.target.value)}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {(selected as string[]).map((value) => (
                          <Chip key={value} label={`${value} employees`} size="small" />
                        ))}
                      </Box>
                    )}
                  >
                    {companySizes.map((size) => (
                      <MenuItem key={size} value={size}>
                        {size} employees
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Industry */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Industry</InputLabel>
                  <Select
                    multiple
                    value={filters.industry}
                    onChange={(e) => handleFilterChange('industry', e.target.value)}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {(selected as string[]).map((value) => (
                          <Chip key={value} label={value} size="small" />
                        ))}
                      </Box>
                    )}
                  >
                    {industries.map((industry) => (
                      <MenuItem key={industry} value={industry}>
                        {industry}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Posted Within */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Posted Within</InputLabel>
                  <Select
                    value={filters.postedWithin}
                    onChange={(e) => handleFilterChange('postedWithin', e.target.value)}
                  >
                    <MenuItem value="">Any time</MenuItem>
                    {postedWithinOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Sort By */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Sort By</InputLabel>
                  <Select
                    value={filters.sortBy}
                    onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                  >
                    <MenuItem value="relevance">Relevance</MenuItem>
                    <MenuItem value="date">Date Posted</MenuItem>
                    <MenuItem value="salary_high">Salary (High to Low)</MenuItem>
                    <MenuItem value="salary_low">Salary (Low to High)</MenuItem>
                    <MenuItem value="company">Company Name</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>

        {/* Action Buttons */}
        <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            startIcon={<SearchIcon />}
            onClick={handleSearch}
            disabled={loading}
            sx={{ flex: 1 }}
          >
            {loading ? 'Searching...' : 'Search Jobs'}
          </Button>
          <Button
            variant="outlined"
            startIcon={<ClearIcon />}
            onClick={handleClearFilters}
          >
            Clear Filters
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
};

export default AdvancedJobSearch;

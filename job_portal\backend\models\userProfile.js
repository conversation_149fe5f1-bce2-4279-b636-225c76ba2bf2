'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class UserProfile extends Model {
    static associate(models) {
      // One-to-one relationship with User
      UserProfile.belongsTo(models.User, {
        foreignKey: 'userId',
        as: 'user'
      });
      
      // Many-to-many relationship with Skills
      UserProfile.belongsToMany(models.Skill, {
        through: 'UserSkills',
        foreignKey: 'userProfileId',
        as: 'skills'
      });
      
      // One-to-many relationship with Experience
      UserProfile.hasMany(models.Experience, {
        foreignKey: 'userProfileId',
        as: 'experiences'
      });
      
      // One-to-many relationship with Education
      UserProfile.hasMany(models.Education, {
        foreignKey: 'userProfileId',
        as: 'education'
      });
    }
  }

  UserProfile.init({
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true
    },
    firstName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    phone: {
      type: DataTypes.STRING,
      validate: {
        isNumeric: true,
        len: [10, 15]
      }
    },
    location: {
      type: DataTypes.STRING
    },
    bio: {
      type: DataTypes.TEXT
    },
    profilePicture: {
      type: DataTypes.STRING // URL to profile picture
    },
    resumeUrl: {
      type: DataTypes.STRING // URL to resume file
    },
    portfolioUrl: {
      type: DataTypes.STRING,
      validate: {
        isUrl: true
      }
    },
    linkedinUrl: {
      type: DataTypes.STRING,
      validate: {
        isUrl: true
      }
    },
    githubUrl: {
      type: DataTypes.STRING,
      validate: {
        isUrl: true
      }
    },
    expectedSalary: {
      type: DataTypes.DECIMAL(10, 2)
    },
    currentSalary: {
      type: DataTypes.DECIMAL(10, 2)
    },
    experienceLevel: {
      type: DataTypes.ENUM('entry', 'junior', 'mid', 'senior', 'lead', 'executive'),
      defaultValue: 'entry'
    },
    availability: {
      type: DataTypes.ENUM('immediate', 'two_weeks', 'one_month', 'three_months'),
      defaultValue: 'immediate'
    },
    workType: {
      type: DataTypes.ENUM('full_time', 'part_time', 'contract', 'freelance', 'internship'),
      defaultValue: 'full_time'
    },
    remotePreference: {
      type: DataTypes.ENUM('remote', 'hybrid', 'onsite', 'no_preference'),
      defaultValue: 'no_preference'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    lastActiveAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'UserProfile',
    tableName: 'user_profiles',
    timestamps: true,
    indexes: [
      {
        fields: ['userId']
      },
      {
        fields: ['location']
      },
      {
        fields: ['experienceLevel']
      },
      {
        fields: ['isActive']
      }
    ]
  });

  return UserProfile;
};

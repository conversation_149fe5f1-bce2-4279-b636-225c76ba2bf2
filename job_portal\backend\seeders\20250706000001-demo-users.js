'use strict';
const bcrypt = require('bcrypt');

module.exports = {
  async up(queryInterface, Sequelize) {
    const saltRounds = 10;
    
    // Create sample users
    const users = [
      // Job Seekers
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', saltRounds),
        role: 'jobseeker',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', saltRounds),
        role: 'jobseeker',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', saltRounds),
        role: 'jobseeker',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', saltRounds),
        role: 'jobseeker',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', saltRounds),
        role: 'jobseeker',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      
      // Employers
      {
        name: 'Tech Corp HR',
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', saltRounds),
        role: 'employer',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'StartupXYZ Recruiter',
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', saltRounds),
        role: 'employer',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Global Solutions HR',
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', saltRounds),
        role: 'employer',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Innovation Labs',
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', saltRounds),
        role: 'employer',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Digital Agency HR',
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', saltRounds),
        role: 'employer',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    await queryInterface.bulkInsert('users', users, {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('users', null, {});
  }
};

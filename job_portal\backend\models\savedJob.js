'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class SavedJob extends Model {
    static associate(models) {
      SavedJob.belongsTo(models.User, {
        foreignKey: 'userId',
        as: 'user'
      });
      
      SavedJob.belongsTo(models.Job, {
        foreignKey: 'jobId',
        as: 'job'
      });
    }
  }

  SavedJob.init({
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    jobId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    notes: {
      type: DataTypes.TEXT
    },
    priority: {
      type: DataTypes.ENUM('low', 'medium', 'high'),
      defaultValue: 'medium'
    },
    status: {
      type: DataTypes.ENUM('saved', 'applied', 'interviewing', 'rejected', 'withdrawn'),
      defaultValue: 'saved'
    }
  }, {
    sequelize,
    modelName: 'SavedJob',
    tableName: 'saved_jobs',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['userId', 'jobId']
      },
      {
        fields: ['userId']
      },
      {
        fields: ['status']
      },
      {
        fields: ['priority']
      }
    ]
  });

  return SavedJob;
};

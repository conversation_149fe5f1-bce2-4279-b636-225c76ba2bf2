'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const companies = [
      {
        userId: 6, // Tech Corp HR
        name: 'TechCorp Solutions',
        description: 'Leading technology company specializing in cloud solutions and enterprise software. We help businesses transform digitally with cutting-edge technology.',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        userId: 7, // StartupXYZ Recruiter
        name: 'StartupXYZ',
        description: 'Fast-growing fintech startup revolutionizing digital payments. Join our dynamic team and help shape the future of finance.',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        userId: 8, // Global Solutions HR
        name: 'Global Solutions Inc',
        description: 'International consulting firm providing strategic business solutions to Fortune 500 companies worldwide.',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        userId: 9, // Innovation Labs
        name: 'Innovation Labs',
        description: 'Research and development company focused on AI, machine learning, and emerging technologies.',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        userId: 10, // Digital Agency HR
        name: 'Creative Digital Agency',
        description: 'Full-service digital marketing agency helping brands create compelling online experiences.',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    await queryInterface.bulkInsert('companies', companies, {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('companies', null, {});
  }
};

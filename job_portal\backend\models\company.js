'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Company extends Model {
    static associate(models) {
      // Belongs to User (employer)
      Company.belongsTo(models.User, {
        foreignKey: 'userId',
        as: 'owner'
      });

      // Has many Jobs
      Company.hasMany(models.Job, {
        foreignKey: 'companyId',
        as: 'jobs'
      });
    }
  }

  Company.init({
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT
    },
    website: {
      type: DataTypes.STRING,
      validate: {
        isUrl: true
      }
    },
    logo: {
      type: DataTypes.STRING // URL to logo
    },
    industry: {
      type: DataTypes.STRING
    },
    companySize: {
      type: DataTypes.ENUM('1-10', '11-50', '51-200', '201-500', '501-1000', '1000+'),
      defaultValue: '1-10'
    },
    foundedYear: {
      type: DataTypes.INTEGER,
      validate: {
        min: 1800,
        max: new Date().getFullYear()
      }
    },
    headquarters: {
      type: DataTypes.STRING
    },
    phone: {
      type: DataTypes.STRING
    },
    email: {
      type: DataTypes.STRING,
      validate: {
        isEmail: true
      }
    },
    linkedinUrl: {
      type: DataTypes.STRING,
      validate: {
        isUrl: true
      }
    },
    twitterUrl: {
      type: DataTypes.STRING,
      validate: {
        isUrl: true
      }
    },
    culture: {
      type: DataTypes.TEXT
    },
    benefits: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      defaultValue: []
    },
    techStack: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      defaultValue: []
    },
    isVerified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    rating: {
      type: DataTypes.DECIMAL(2, 1),
      defaultValue: 0.0,
      validate: {
        min: 0.0,
        max: 5.0
      }
    },
    reviewCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    }
  }, {
    sequelize,
    modelName: 'Company',
    tableName: 'companies',
    timestamps: true,
    indexes: [
      {
        fields: ['userId']
      },
      {
        fields: ['name']
      },
      {
        fields: ['industry']
      },
      {
        fields: ['companySize']
      },
      {
        fields: ['isVerified']
      },
      {
        fields: ['isActive']
      }
    ]
  });

  return Company;
};
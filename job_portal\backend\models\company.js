'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Company extends Model {
    static associate(models) {
      // Belongs to User (employer)
      Company.belongsTo(models.User, {
        foreignKey: 'userId',
        as: 'owner'
      });

      // Has many Jobs
      Company.hasMany(models.Job, {
        foreignKey: 'companyId',
        as: 'jobs'
      });
    }
  }

  Company.init({
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT
    },
    website: {
      type: DataTypes.STRING
    },
    location: {
      type: DataTypes.STRING
    }
  }, {
    sequelize,
    modelName: 'Company',
    tableName: 'companies',
    timestamps: true,
    indexes: [
      {
        fields: ['userId']
      },
      {
        fields: ['name']
      },
      {
        fields: ['industry']
      },
      {
        fields: ['companySize']
      },
      {
        fields: ['isVerified']
      },
      {
        fields: ['isActive']
      }
    ]
  });

  return Company;
};
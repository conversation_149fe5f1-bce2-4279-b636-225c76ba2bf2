'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('skills', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      category: {
        type: Sequelize.ENUM(
          'programming_languages',
          'frameworks',
          'databases',
          'tools',
          'soft_skills',
          'certifications',
          'languages'
        ),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add indexes
    await queryInterface.addIndex('skills', ['name']);
    await queryInterface.addIndex('skills', ['category']);
    await queryInterface.addIndex('skills', ['isActive']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('skills');
  }
};

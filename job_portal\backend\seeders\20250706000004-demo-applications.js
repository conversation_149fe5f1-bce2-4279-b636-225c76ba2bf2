'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const applications = [
      // <PERSON>'s Applications
      {
        userId: 1,
        jobId: 1, // Senior Full Stack Developer at TechCorp
        coverLetter: 'I am excited to apply for the Senior Full Stack Developer position. With over 6 years of experience in web development, I have extensive knowledge of React, Node.js, and PostgreSQL. I am passionate about creating scalable solutions and would love to contribute to TechCorp\'s innovative projects.',
        status: 'pending',
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
      },
      {
        userId: 1,
        jobId: 4, // Frontend Developer at StartupXYZ
        coverLetter: 'I am interested in the Frontend Developer role at StartupXYZ. My experience with React and TypeScript aligns perfectly with your requirements. I am excited about the opportunity to work in a fast-paced fintech environment.',
        status: 'reviewed',
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)  // 1 day ago
      },
      {
        userId: 1,
        jobId: 14, // Remote Software Engineer
        coverLetter: 'I am very interested in the Remote Software Engineer position. I have experience working in distributed teams and am comfortable with remote collaboration tools. My technical skills and self-motivation make me a great fit for this role.',
        status: 'interviewed',
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
        updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)  // 3 days ago
      },

      // <PERSON> Smith's Applications
      {
        userId: 2,
        jobId: 11, // UX/UI Designer at Creative Digital Agency
        coverLetter: 'I am thrilled to apply for the UX/UI Designer position. With a strong background in user experience design and proficiency in Figma and Adobe Creative Suite, I am confident I can create intuitive and visually appealing designs for your clients.',
        status: 'pending',
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
      },
      {
        userId: 2,
        jobId: 3, // Product Manager at TechCorp
        coverLetter: 'I am excited about the Product Manager opportunity at TechCorp. My experience in product development and agile methodologies, combined with my analytical skills, would allow me to effectively lead product initiatives and work with cross-functional teams.',
        status: 'reviewed',
        createdAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), // 4 days ago
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)  // 2 days ago
      },

      // Mike Johnson's Applications
      {
        userId: 3,
        jobId: 2, // DevOps Engineer at TechCorp
        coverLetter: 'I am applying for the DevOps Engineer position. I have extensive experience with AWS, Docker, and Kubernetes, and I am passionate about building scalable infrastructure. I would love to contribute to TechCorp\'s cloud initiatives.',
        status: 'offered',
        createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)   // 1 day ago
      },
      {
        userId: 3,
        jobId: 5, // Backend Developer at StartupXYZ
        coverLetter: 'I am interested in the Backend Developer role at StartupXYZ. My experience with Python and Django, along with my knowledge of microservices architecture, makes me a strong candidate for this position.',
        status: 'pending',
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
      },

      // Sarah Wilson's Applications
      {
        userId: 4,
        jobId: 6, // Data Scientist at StartupXYZ
        coverLetter: 'I am excited to apply for the Data Scientist position. With my PhD in Statistics and experience with machine learning frameworks, I am well-equipped to analyze financial data and improve your algorithms.',
        status: 'interviewed',
        createdAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), // 6 days ago
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)  // 2 days ago
      },
      {
        userId: 4,
        jobId: 9, // Machine Learning Engineer at Innovation Labs
        coverLetter: 'I am very interested in the Machine Learning Engineer position at Innovation Labs. My experience with TensorFlow and PyTorch, combined with my research background, would allow me to contribute effectively to your AI initiatives.',
        status: 'pending',
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
      },

      // David Brown's Applications
      {
        userId: 5,
        jobId: 7, // Business Analyst at Global Solutions
        coverLetter: 'I am applying for the Business Analyst position. My strong analytical skills and experience with business process modeling make me a great fit for this role. I am excited about the opportunity to work with Fortune 500 clients.',
        status: 'reviewed',
        createdAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000), // 8 days ago
        updatedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000)  // 4 days ago
      },
      {
        userId: 5,
        jobId: 8, // Project Manager at Global Solutions
        coverLetter: 'I am interested in the Project Manager role at Global Solutions. With my PMP certification and 4 years of project management experience, I am confident I can lead cross-functional teams and deliver projects successfully.',
        status: 'rejected',
        createdAt: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000), // 12 days ago
        updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)   // 5 days ago
      },
      {
        userId: 5,
        jobId: 12, // Digital Marketing Specialist
        coverLetter: 'I am excited about the Digital Marketing Specialist position. My experience with Google Ads and Facebook Ads, along with my knowledge of analytics tools, would help drive successful marketing campaigns for your clients.',
        status: 'pending',
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
      }
    ];

    await queryInterface.bulkInsert('job_applications', applications, {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('job_applications', null, {});
  }
};

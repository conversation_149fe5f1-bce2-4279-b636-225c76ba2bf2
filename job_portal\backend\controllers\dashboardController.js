const { User, Job, Application, Company, UserProfile, SavedJob, Notification } = require('../models');
const { Op, fn, col, literal } = require('sequelize');

// Get job seeker dashboard data
const getJobSeekerDashboard = async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user profile completion percentage
    const profile = await UserProfile.findOne({ where: { userId } });
    const profileCompletion = calculateProfileCompletion(profile);

    // Get application statistics
    const applicationStats = await Application.findAll({
      where: { userId },
      attributes: [
        'status',
        [fn('COUNT', col('id')), 'count']
      ],
      group: ['status']
    });

    // Get recent applications
    const recentApplications = await Application.findAll({
      where: { userId },
      include: [
        {
          model: Job,
          attributes: ['id', 'title', 'location'],
          include: [
            {
              model: Company,
              attributes: ['id', 'name', 'logo']
            }
          ]
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: 5
    });

    // Get saved jobs count
    const savedJobsCount = await SavedJob.count({ where: { userId } });

    // Get unread notifications count
    const unreadNotifications = await Notification.count({
      where: { userId, isRead: false }
    });

    // Get job recommendations (simplified - based on user skills and preferences)
    const recommendedJobs = await getJobRecommendations(userId);

    // Get application activity over time (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const applicationActivity = await Application.findAll({
      where: {
        userId,
        createdAt: { [Op.gte]: thirtyDaysAgo }
      },
      attributes: [
        [fn('DATE', col('createdAt')), 'date'],
        [fn('COUNT', col('id')), 'count']
      ],
      group: [fn('DATE', col('createdAt'))],
      order: [[fn('DATE', col('createdAt')), 'ASC']]
    });

    res.json({
      success: true,
      dashboard: {
        profileCompletion,
        applicationStats: formatApplicationStats(applicationStats),
        recentApplications,
        savedJobsCount,
        unreadNotifications,
        recommendedJobs,
        applicationActivity
      }
    });
  } catch (error) {
    console.error('Get job seeker dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get employer dashboard data
const getEmployerDashboard = async (req, res) => {
  try {
    const userId = req.user.id;

    // Get company information
    const company = await Company.findOne({ where: { userId } });
    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company profile not found'
      });
    }

    // Get job statistics
    const jobStats = await Job.findAll({
      where: { companyId: company.id },
      attributes: [
        'isActive',
        [fn('COUNT', col('id')), 'count'],
        [fn('SUM', col('applicationCount')), 'totalApplications'],
        [fn('SUM', col('viewCount')), 'totalViews']
      ],
      group: ['isActive']
    });

    // Get recent applications
    const recentApplications = await Application.findAll({
      include: [
        {
          model: Job,
          where: { companyId: company.id },
          attributes: ['id', 'title']
        },
        {
          model: User,
          attributes: ['id', 'name', 'email']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: 10
    });

    // Get top performing jobs
    const topJobs = await Job.findAll({
      where: { companyId: company.id },
      attributes: ['id', 'title', 'applicationCount', 'viewCount', 'createdAt'],
      order: [['applicationCount', 'DESC']],
      limit: 5
    });

    // Get application status distribution
    const applicationStatusStats = await Application.findAll({
      include: [
        {
          model: Job,
          where: { companyId: company.id },
          attributes: []
        }
      ],
      attributes: [
        'status',
        [fn('COUNT', col('Application.id')), 'count']
      ],
      group: ['status']
    });

    // Get hiring funnel data (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const hiringFunnel = await Application.findAll({
      include: [
        {
          model: Job,
          where: { companyId: company.id },
          attributes: []
        }
      ],
      where: {
        createdAt: { [Op.gte]: thirtyDaysAgo }
      },
      attributes: [
        [fn('DATE', col('Application.createdAt')), 'date'],
        'status',
        [fn('COUNT', col('Application.id')), 'count']
      ],
      group: [fn('DATE', col('Application.createdAt')), 'status'],
      order: [[fn('DATE', col('Application.createdAt')), 'ASC']]
    });

    res.json({
      success: true,
      dashboard: {
        company,
        jobStats: formatJobStats(jobStats),
        recentApplications,
        topJobs,
        applicationStatusStats: formatApplicationStats(applicationStatusStats),
        hiringFunnel
      }
    });
  } catch (error) {
    console.error('Get employer dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Helper function to calculate profile completion
const calculateProfileCompletion = (profile) => {
  if (!profile) return 0;

  const fields = [
    'firstName', 'lastName', 'phone', 'location', 'bio',
    'experienceLevel', 'workType', 'remotePreference'
  ];
  
  const completedFields = fields.filter(field => profile[field] && profile[field] !== '');
  return Math.round((completedFields.length / fields.length) * 100);
};

// Helper function to format application stats
const formatApplicationStats = (stats) => {
  const formatted = {
    pending: 0,
    reviewed: 0,
    interviewed: 0,
    offered: 0,
    hired: 0,
    rejected: 0,
    total: 0
  };

  stats.forEach(stat => {
    const status = stat.dataValues.status;
    const count = parseInt(stat.dataValues.count);
    formatted[status] = count;
    formatted.total += count;
  });

  return formatted;
};

// Helper function to format job stats
const formatJobStats = (stats) => {
  const formatted = {
    active: 0,
    inactive: 0,
    totalApplications: 0,
    totalViews: 0
  };

  stats.forEach(stat => {
    const isActive = stat.dataValues.isActive;
    const count = parseInt(stat.dataValues.count);
    const applications = parseInt(stat.dataValues.totalApplications) || 0;
    const views = parseInt(stat.dataValues.totalViews) || 0;

    if (isActive) {
      formatted.active = count;
    } else {
      formatted.inactive = count;
    }
    
    formatted.totalApplications += applications;
    formatted.totalViews += views;
  });

  return formatted;
};

// Helper function to get job recommendations
const getJobRecommendations = async (userId) => {
  try {
    const profile = await UserProfile.findOne({
      where: { userId },
      include: [
        {
          model: Skill,
          as: 'skills',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!profile || !profile.skills.length) {
      return [];
    }

    const skillIds = profile.skills.map(skill => skill.id);

    // Find jobs that match user skills and preferences
    const jobs = await Job.findAll({
      where: {
        isActive: true,
        experienceLevel: profile.experienceLevel,
        workType: profile.workType
      },
      include: [
        {
          model: Skill,
          as: 'skills',
          where: { id: { [Op.in]: skillIds } },
          through: { attributes: [] }
        },
        {
          model: Company,
          attributes: ['id', 'name', 'logo']
        }
      ],
      limit: 5,
      order: [['createdAt', 'DESC']]
    });

    return jobs;
  } catch (error) {
    console.error('Get job recommendations error:', error);
    return [];
  }
};

module.exports = {
  getJobSeekerDashboard,
  getEmployerDashboard
};
